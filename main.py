from ultralytics import YOLO
from Globals import *
from test_for_function import test_for_exchange_teacher, test_for_exchange_student, test_for_exchange_student_back
import torch
if __name__ == '__main__':

    # 进行原版模型的正常训练：
    if not bool_distill:
        # 加载模型
        model = YOLO(model_file)  # 从头开始构建新模型

        # 训练模型
        model.train(data=dataset, epochs=epochs, save=bool_save, save_period=save_period, resume=bool_resume, device=device, batch=batch_size)

        # 在验证集上评估模型性能
        metrics = model.val(data=dataset)

        # 对图像进行预测
        # results = model(test_graph)

    # 进行蒸馏训练：
    elif bool_distill:
        # P0:第一阶段准备阶段
        if phase == 0:
            # [1]可选:将学生预训练模型转变为适合的格式
            # test_for_exchange_student(path_student, path_student_prepare)
            # [2]将教师预训练模型转变为适合的格式
            test_for_exchange_teacher(path_teacher, path_teacher_prepare)
        # P1:训练开始
        if phase == 1:
            # [1]加载模型
            model = YOLO(model_file)
            # [2]可选:加载预训练学生模型
            # model.load_state_dict(torch.load(path_student_prepare), strict=False)
            # [3]加载预训练教师模型
            model.load_state_dict(torch.load(path_teacher_prepare), strict=False)
            # [4]进行模型训练
            model.train(data=dataset, epochs=epochs, save=bool_save, save_period=save_period, freeze=teacher_peer_list,
                        resume=bool_resume, device=device, batch=batch_size)
            # [5]将训练好得模型提取出来
            test_for_exchange_student_back(model_based_file, model, final)
            # [6]可选:检验训练集准确度
            model.val(data=dataset)
            # [7]可选:要求程序结束时自动关机以节省运算资源
            # import os
            # os.system("shutdown /s /t 0")

